import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Plus, AlertCircle } from 'lucide-react';
import { availableTables } from '../../SampleSandboxData';
import { RuleMetricEquation } from '../../../Red Flags/Rules/components/RuleMetricEquation';
import { BaseMetricCondition, MetricEquation } from '@/app/types';
import { SectionContainer } from './SectionContainer';
import { AdvancedColumnSelector } from './AdvancedColumnSelector';
import { ColumnStatistics } from '@/app/types';

interface AnalysisSectionProps {
  selectedTable: string;
  analysisType: 'rule' | 'metric';
  ruleAnalysis: MetricEquation;
  metricAnalysis: Array<{
    column: string;
    description?: string;
    statistics?: ColumnStatistics;
  }>;
  onAnalysisTypeChange: (type: 'rule' | 'metric') => void;
  onRuleAnalysisChange: (analysis: MetricEquation) => void;
  onMetricAnalysisChange: (columns: Array<{
    column: string;
    description?: string;
    statistics?: ColumnStatistics;
  }>) => void;
  onColumnStatsRequest?: (column: string) => Promise<ColumnStatistics>;
}

export const AnalysisSection: React.FC<AnalysisSectionProps> = ({
  selectedTable,
  analysisType,
  ruleAnalysis,
  metricAnalysis,
  onAnalysisTypeChange,
  onRuleAnalysisChange,
  onMetricAnalysisChange,
  onColumnStatsRequest
}) => {
  const selectedTableData = availableTables.find(t => t.id === selectedTable);
  const columns = selectedTableData ? Object.keys(selectedTableData.schema.shape) : [];

  const handleValueChange = useCallback((value: string) => {
    onAnalysisTypeChange(value as 'rule' | 'metric');
  }, [onAnalysisTypeChange]);

  const handleAddCondition = useCallback(() => {
    if (columns.length > 0) {
      const newCondition: BaseMetricCondition = {
        metric_name: columns[0],
        operation: '>=',
        value: '',
        type: 'string'
      };
      onRuleAnalysisChange({
        ...ruleAnalysis,
        conditions: [...ruleAnalysis.conditions, newCondition]
      });
    }
  }, [columns, ruleAnalysis, onRuleAnalysisChange]);

  return (
    <div className="space-y-4">
      <Tabs value={analysisType} onValueChange={handleValueChange}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="rule">Rule Analysis</TabsTrigger>
          <TabsTrigger value="metric">Metric Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="rule">
          <SectionContainer 
            title="Define Rule"
            rightElement={
              <Button
                variant="outline"
                size="sm"
                onClick={handleAddCondition}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Condition
              </Button>
            }
          >
            <RuleMetricEquation
              equation={ruleAnalysis}
              isEditing={true}
              onMetricUpdate={(metric) => {
                if ('metric_name' in metric) {
                  const newEquation = {
                    ...ruleAnalysis,
                    conditions: ruleAnalysis.conditions.map(condition => 
                      'metric_name' in condition && condition.metric_name === metric.metric_name
                        ? metric
                        : condition
                    )
                  };
                  onRuleAnalysisChange(newEquation);
                }
              }}
            />

            {ruleAnalysis.conditions.length === 0 && (
              <div className="text-sm text-muted-foreground text-center py-4">
                No conditions added. Click "Add Condition" to create one.
              </div>
            )}
          </SectionContainer>
        </TabsContent>

        <TabsContent value="metric">
          <SectionContainer title="Select Metric Columns">
            <AdvancedColumnSelector
              selectedTable={selectedTable}
              selectedColumns={metricAnalysis}
              onColumnsChange={onMetricAnalysisChange}
              onColumnStatsRequest={onColumnStatsRequest}
            />
          </SectionContainer>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 