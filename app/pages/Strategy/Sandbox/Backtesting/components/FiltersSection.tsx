import React, { useCallback } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Plus, Wand2 } from 'lucide-react';
import { availableTables, TimeframeFilter, PopulationFilter, ColumnConfig } from '../../SampleSandboxData';
import { RuleMetricEquation } from '../../../Red Flags/Rules/components/RuleMetricEquation';
import { BaseMetricCondition, MetricEquation } from '@/app/types';
import { SectionContainer } from './SectionContainer';
import { useBacktestingStore } from '@/app/store/backtesting/backtestingStore';

interface FiltersSectionProps {
  selectedTable: string;
  columnConfig: ColumnConfig;
  timeframeFilter: TimeframeFilter;
  populationFilters: PopulationFilter[];
  onTimeframeUpdate: (filter: TimeframeFilter) => void;
  onPopulationFiltersUpdate: (filters: PopulationFilter[]) => void;
  onTimeframeValidityChange?: (isValid: boolean) => void;
  onAutoDetectTimeframe?: (fn: () => void) => void;
}

// Add type for table row data
type TableRow = {
  [key: string]: string | number | boolean;
};

export const FiltersSection: React.FC<FiltersSectionProps> = ({
  selectedTable,
  columnConfig,
  timeframeFilter,
  populationFilters,
  onTimeframeUpdate,
  onPopulationFiltersUpdate,
  onTimeframeValidityChange,
  onAutoDetectTimeframe
}) => {
  const { data, fetchData, tables, fetchFieldValues } = useBacktestingStore();
  const selectedTableData = tables.find(t => t.id === selectedTable);
  const columns = selectedTableData ? Object.keys(selectedTableData.schema.shape) : [];

  // Check if datetime column is configured
  const hasDatetimeColumn = Boolean(columnConfig.datetime);

  // Check if timeframe values are empty
  const hasEmptyTimeframeValues = !timeframeFilter.startDate || !timeframeFilter.endDate;

  // Notify parent component about timeframe validity changes
  React.useEffect(() => {
    if (hasDatetimeColumn && onTimeframeValidityChange) {
      onTimeframeValidityChange(!hasEmptyTimeframeValues);
    }
  }, [hasDatetimeColumn, hasEmptyTimeframeValues, onTimeframeValidityChange]);

  // Convert timeframe filter to metric equation format only if datetime column is configured
  const timeframeEquation: MetricEquation = hasDatetimeColumn ? {
    operator: 'AND',
    conditions: [
      {
        metric_name: columnConfig.datetime,
        operation: '>=',
        value: timeframeFilter.startDate,
        type: 'string'
      } as BaseMetricCondition,
      {
        metric_name: columnConfig.datetime,
        operation: '<=',
        value: timeframeFilter.endDate,
        type: 'string'
      } as BaseMetricCondition
    ]
  } : {
    operator: 'AND',
    conditions: []
  };

  // Handle timeframe updates
  const handleTimeframeUpdate = (equation: MetricEquation) => {
    if (!hasDatetimeColumn) return;

    if ('conditions' in equation && equation.conditions.length === 2) {
      const [startCondition, endCondition] = equation.conditions as BaseMetricCondition[];
      if (startCondition.operation === '>=' && endCondition.operation === '<=') {
        onTimeframeUpdate({
          startDate: startCondition.value as string,
          endDate: endCondition.value as string
        });
      }
    }
  };

  // Convert population filters to metric equation format for RuleMetricEquation
  const populationEquation: MetricEquation = {
    operator: 'AND',
    conditions: populationFilters.map(filter => ({
      metric_name: filter.column,
      operation: filter.operator,
      value: Array.isArray(filter.value) ? filter.value.join(';') : filter.value,
      type: Array.isArray(filter.value) ? 'string' : typeof filter.value === 'number' ? 'numeric' : 'string'
    })) as BaseMetricCondition[]
  };

  const handlePopulationUpdate = (equation: MetricEquation) => {
    if ('conditions' in equation) {
      const newFilters = equation.conditions
        .filter((condition): condition is BaseMetricCondition => 'metric_name' in condition)
        .map(condition => {
          const value = condition.type === 'string' && typeof condition.value === 'string' && condition.value.includes(';')
            ? condition.value.split(';').filter(Boolean)
            : condition.value;

          // Ensure value is of correct type
          const typedValue: string | number | string[] =
            typeof value === 'boolean' ? value.toString() :
            Array.isArray(value) ? value :
            value;

          return {
            column: condition.metric_name,
            operator: condition.operation as '>=' | '<=' | 'is in',
            value: typedValue
          };
        });
      onPopulationFiltersUpdate(newFilters);
    }
  };

  // Function to auto-detect timeframe
  const handleAutoDetectTimeframe = useCallback(async () => {
    if (!selectedTableData || !columnConfig.datetime) return;

    try {
      const response = await fetchFieldValues(selectedTable, columnConfig.datetime, 'min_max');
      if ('min_value' in response && 'max_value' in response) {
        onTimeframeUpdate({
          startDate: response.min_value.split('T')[0],
          endDate: response.max_value.split('T')[0]
        });
      }
    } catch (error) {
      console.error('Failed to auto-detect timeframe:', error);
    }
  }, [selectedTableData, columnConfig.datetime, onTimeframeUpdate, selectedTable, fetchFieldValues]);

  // Function to fetch population filter values
  const handlePopulationFilterValues = useCallback(async (column: string) => {
    try {
      const response = await fetchFieldValues(selectedTable, column, 'all');
      if ('distinct_values' in response) {
        return response.distinct_values;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch population filter values:', error);
      return [];
    }
  }, [selectedTable, fetchFieldValues]);

  // Make handleAutoDetectTimeframe available to parent
  React.useEffect(() => {
    if (onAutoDetectTimeframe) {
      onAutoDetectTimeframe(handleAutoDetectTimeframe);
    }
  }, [onAutoDetectTimeframe, handleAutoDetectTimeframe]);

  return (
    <div className="space-y-4">
      {/* Timeframe Filter */}
      <SectionContainer
        title="Timeframe Filter"
      >
        {hasDatetimeColumn ? (
          <RuleMetricEquation
            equation={timeframeEquation}
            isEditing={true}
            onMetricUpdate={(metric, oldMetricName) => {
              if ('metric_name' in metric && 'operation' in metric) {
                const newEquation = {
                  ...timeframeEquation,
                  conditions: timeframeEquation.conditions.map(c => {
                    const condition = c as BaseMetricCondition;
                    // For timeframe filters, field changes shouldn't happen since no availableFields is provided
                    // But handle it for consistency
                    const searchName = oldMetricName || metric.metric_name;
                    return condition.metric_name === searchName && condition.operation === metric.operation
                      ? { ...condition, metric_name: metric.metric_name, value: metric.value }
                      : condition;
                  })
                };
                handleTimeframeUpdate(newEquation);
              }
            }}
            onFieldValuesRequest={handlePopulationFilterValues}
          />
        ) : (
          <div className="text-sm text-muted-foreground text-center py-4">
            Please select a datetime column in the Configuration section to enable timeframe filtering
          </div>
        )}
      </SectionContainer>

      {/* Population Filters */}
      <SectionContainer
        title="Population Filters"
        rightElement={
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPopulationFiltersUpdate([
              ...populationFilters,
              { column: columns[0] || '', operator: '>=', value: '' }
            ])}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Filter
          </Button>
        }
      >
        <RuleMetricEquation
          equation={populationEquation}
          isEditing={true}
          availableFields={columns}
          onMetricUpdate={(metric, oldMetricName) => {
            if ('metric_name' in metric) {
              const value = metric.type === 'string' && typeof metric.value === 'string' && metric.value.includes(';')
                ? metric.value.split(';').filter(Boolean)
                : metric.value;

              const typedValue: string | number | string[] =
                typeof value === 'boolean' ? value.toString() :
                Array.isArray(value) ? value :
                value;

              // If oldMetricName is provided, this is a field change - find by old name
              if (oldMetricName) {
                const newFilters = populationFilters.map(f =>
                  f.column === oldMetricName ? {
                    column: metric.metric_name, // Update the column name (key)
                    operator: metric.operation as '>=' | '<=' | 'is in',
                    value: typedValue
                  } : f
                );
                onPopulationFiltersUpdate(newFilters);
              } else {
                // Regular update - find by current metric name
                const newFilters = populationFilters.map(f =>
                  f.column === metric.metric_name ? {
                    ...f,
                    operator: metric.operation as '>=' | '<=' | 'is in',
                    value: typedValue
                  } : f
                );
                onPopulationFiltersUpdate(newFilters);
              }
            }
          }}
          onFieldValuesRequest={handlePopulationFilterValues}
        />

        {populationFilters.length === 0 && (
          <div className="text-sm text-muted-foreground text-center py-4">
            No population filters added. Click "Add Filter" to create one.
          </div>
        )}
      </SectionContainer>
    </div>
  );
};