import React, { useState, useEffect } from 'react';
import { BaseMetricCondition, CompositeMetricCondition, MetricEquation } from '@/app/types';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { X } from 'lucide-react';

interface RuleMetricEquationProps {
  equation: MetricEquation;
  isEditing?: boolean;
  onMetricUpdate?: (metric: BaseMetricCondition, oldMetricName?: string) => void;
  onFieldValuesRequest?: (column: string) => Promise<string[]>;
  availableFields?: string[];
}

const NUMERIC_OPERATORS = [">", "<", ">=", "<=", "=", "!="];
const STRING_OPERATORS = ["is in", "is not in"];

export const RuleMetricEquation: React.FC<RuleMetricEquationProps> = ({
  equation,
  isEditing = false,
  onMetricUpdate,
  onFieldValuesRequest,
  availableFields = []
}) => {
  const [stringInputs, setStringInputs] = useState<Record<string, string>>({});
  const [fieldValues, setFieldValues] = useState<Record<string, string[]>>({});
  const [loadingValues, setLoadingValues] = useState<Record<string, boolean>>({});
  const [activeField, setActiveField] = useState<string | null>(null);

  // Sync component state with props
  useEffect(() => {
    // Find the first condition with a metric_name
    const currentCondition = equation.conditions.find(
      (condition): condition is BaseMetricCondition =>
        'metric_name' in condition
    );

    if (currentCondition?.metric_name) {
      console.log('Syncing activeField with condition:', currentCondition.metric_name);
      setActiveField(currentCondition.metric_name);
    }
  }, [equation.conditions]);

  // Debug logging for state changes
  useEffect(() => {
    console.log('State Update:', {
      equation,
      activeField,
      fieldValues,
      stringInputs,
      loadingValues
    });
  }, [equation, activeField, fieldValues, stringInputs, loadingValues]);

  // Load values when active field changes
  useEffect(() => {
    if (activeField && onFieldValuesRequest) {
      const loadFieldValues = async () => {
        // Only load if we don't have values or if they're empty
        if (loadingValues[activeField] || (fieldValues[activeField] && fieldValues[activeField].length > 0)) {
          return;
        }

        console.log('Loading values for field:', activeField);
        setLoadingValues(prev => ({ ...prev, [activeField]: true }));

        try {
          const values = await onFieldValuesRequest(activeField);
          console.log('Received values for field:', activeField, values);
          setFieldValues(prev => {
            const newState = { ...prev, [activeField]: values };
            console.log('Updated fieldValues:', newState);
            return newState;
          });
        } catch (error) {
          console.error('Failed to load field values:', error);
        } finally {
          setLoadingValues(prev => ({ ...prev, [activeField]: false }));
        }
      };

      loadFieldValues();
    }
  }, [activeField, fieldValues, loadingValues, onFieldValuesRequest]);

  const getOperatorsByType = (type: 'numeric' | 'string' | 'boolean') => {
    switch (type) {
      case 'numeric':
        return NUMERIC_OPERATORS;
      case 'string':
        return STRING_OPERATORS;
      case 'boolean':
        return ["="];
      default:
        return [];
    }
  };

  const handleAddValue = (condition: BaseMetricCondition, value: string) => {
    if (!value.trim()) return;

    const currentValues = condition.value.toString().split(';').filter(Boolean);
    const newValues = [...currentValues, value.trim()];
    const newValue = newValues.join(';');

    console.log('Adding value:', {
      field: condition.metric_name,
      currentValues,
      newValue,
      value
    });

    // Update parent component first
    const updatedCondition = {
      ...condition,
      value: newValue
    };
    onMetricUpdate?.(updatedCondition);

    // Then update local state
    setStringInputs(prev => {
      const newState = { ...prev, [condition.metric_name]: '' };
      console.log('Cleared input for field:', condition.metric_name);
      return newState;
    });
  };

  const handleRemoveValue = (condition: BaseMetricCondition, indexToRemove: number) => {
    const currentValues = condition.value.toString().split(';').filter(Boolean);
    const newValues = currentValues.filter((_, index) => index !== indexToRemove);
    const newValue = newValues.join(';');

    console.log('Removing value:', {
      field: condition.metric_name,
      currentValues,
      newValues,
      indexToRemove
    });

    // Update parent component
    const updatedCondition = {
      ...condition,
      value: newValue
    };
    onMetricUpdate?.(updatedCondition);
  };

  const handleFieldChange = (oldField: string, newField: string, condition: BaseMetricCondition) => {
    console.log('Field change:', {
      oldField,
      newField,
      currentFieldValues: fieldValues[oldField],
      currentStringInput: stringInputs[oldField]
    });

    // Update parent component first, passing the old field name for identification
    const updatedCondition = {
      ...condition,
      metric_name: newField,
      value: '' // Reset value when field changes
    };
    onMetricUpdate?.(updatedCondition, oldField);

    // Reset all local state
    setFieldValues({});
    setStringInputs({});
    setLoadingValues({});

    // Set active field last to trigger value loading
    setActiveField(newField);
  };

  const renderStringValueInput = (condition: BaseMetricCondition) => {
    // Use the value from props (equation) instead of local state
    const values = condition.value.toString().split(';').filter(Boolean);
    const inputValue = stringInputs[condition.metric_name] || '';
    const availableValues = fieldValues[condition.metric_name] || [];
    const isLoading = loadingValues[condition.metric_name];

    console.log('Rendering string input:', {
      field: condition.metric_name,
      values,
      inputValue,
      availableValues,
      isLoading,
      conditionValue: condition.value
    });

    return (
      <div className="space-y-2">
        <div className="flex flex-wrap gap-2">
          {values.map((value, index) => (
            <Badge
              key={`${condition.metric_name}-${value}-${index}`}
              variant="secondary"
              className="flex items-center gap-1"
            >
              {value}
              <X
                className="h-3 w-3 cursor-pointer hover:text-destructive"
                onClick={() => handleRemoveValue(condition, index)}
              />
            </Badge>
          ))}
        </div>
        <div className="flex gap-2">
          <Select
            value={inputValue}
            onValueChange={(value) => {
              if (value && value !== '__loading__' && value !== '__no_values__') {
                handleAddValue(condition, value);
              }
            }}
            onOpenChange={(open) => {
              if (open && condition.metric_name !== activeField) {
                setActiveField(condition.metric_name);
              }
            }}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select or type a value..." />
            </SelectTrigger>
            <SelectContent>
              {isLoading ? (
                <SelectItem value="__loading__" disabled>Loading values...</SelectItem>
              ) : (
                <>
                  {availableValues.map(value => (
                    <SelectItem
                      key={`${condition.metric_name}-${value}`}
                      value={value || '__empty__'}
                    >
                      {value || '(empty)'}
                    </SelectItem>
                  ))}
                  {availableValues.length === 0 && (
                    <SelectItem value="__no_values__" disabled>No values available</SelectItem>
                  )}
                </>
              )}
            </SelectContent>
          </Select>
          <Input
            value={inputValue}
            onChange={(e) => {
              const newValue = e.target.value;
              console.log('Input change:', {
                field: condition.metric_name,
                newValue
              });
              setStringInputs(prev => ({
                ...prev,
                [condition.metric_name]: newValue
              }));
            }}
            placeholder="Or type a value..."
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddValue(condition, inputValue);
              }
            }}
            className="w-full"
          />
        </div>
      </div>
    );
  };

  const renderCondition = (condition: BaseMetricCondition | CompositeMetricCondition) => {
    if ('metric_name' in condition) {
      // Set active field when rendering a condition
      if (condition.metric_name !== activeField) {
        console.log('Setting active field from render:', condition.metric_name);
        setActiveField(condition.metric_name);
      }

      return (
        <div className="flex items-center gap-2 p-2 border rounded-md bg-background">
          {isEditing && availableFields.length > 0 ? (
            <Select
              value={condition.metric_name}
              onValueChange={(value) => {
                handleFieldChange(condition.metric_name, value, condition);
              }}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select field..." />
              </SelectTrigger>
              <SelectContent>
                {availableFields.map(field => (
                  <SelectItem key={field} value={field}>{field}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Badge variant="outline">{condition.metric_name}</Badge>
          )}
          {isEditing ? (
            <>
              {condition.type !== 'boolean' && (
                <Select
                  value={condition.operation}
                  onValueChange={(value) =>
                    onMetricUpdate?.({
                      ...condition,
                      operation: value
                    })
                  }
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue>{condition.operation}</SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {getOperatorsByType(condition.type).map(op => (
                      <SelectItem key={op} value={op}>{op}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {condition.type === 'boolean' ? (
                <Select
                  value={condition.value.toString()}
                  onValueChange={(value) =>
                    onMetricUpdate?.({
                      ...condition,
                      value: value === 'true'
                    })
                  }
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Select value" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">True</SelectItem>
                    <SelectItem value="false">False</SelectItem>
                  </SelectContent>
                </Select>
              ) : condition.type === 'string' ? (
                renderStringValueInput(condition)
              ) : (
                <Input
                  type="number"
                  value={condition.value.toString()}
                  onChange={(e) =>
                    onMetricUpdate?.({
                      ...condition,
                      value: Number(e.target.value)
                    })
                  }
                  className="w-32"
                />
              )}
            </>
          ) : (
            <>
              {condition.type !== 'boolean' && (
                <span className="font-mono">{condition.operation}</span>
              )}
              {condition.type === 'string' ? (
                <div className="flex flex-wrap gap-1">
                  {condition.value.toString().split(';').filter(Boolean).map((value, index) => (
                    <Badge key={index} variant="secondary">
                      {value}
                    </Badge>
                  ))}
                </div>
              ) : (
                <span className="font-mono">{condition.value.toString()}</span>
              )}
            </>
          )}
        </div>
      );
    }

    return (
      <div className="border-l-2 pl-4 my-2">
        {condition.conditions.map((cond, index) => (
          <React.Fragment key={index}>
            {renderCondition(cond)}
            {index < condition.conditions.length - 1 && (
              <Badge variant="secondary" className="my-2">
                {condition.operator}
              </Badge>
            )}
          </React.Fragment>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {renderCondition(equation)}
    </div>
  );
};

export default RuleMetricEquation;